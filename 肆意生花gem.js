//以下为乐谱转js专有模板
// <PERSON>ript for "肆意生花"
// Tempo: J=72, Time: 4/4
// Note: Some rhythms from the source image were ambiguous and have been interpreted to fit the time signature.

let t0 = 840, // Quarter note ( quarter note = 60,000 / 72 BPM = 833.3ms)
    t1 = 420, // Eighth note
    t2 = 1680, // Half note
    t3 = 100, // Fixed interval*
    pT = 35, // Default press time
    sW = true; // Whether to show floating buttons
let s = 1,
    progressNow = 0,
    speedControl = 1,
    xy = [],
    zuobiaoPath = "/sdcard/脚本/zuobiao21.txt";
if (files.exists(zuobiaoPath)) { //如果机型适配过
    eval(files.read(zuobiaoPath)); //快速适配分辨率
} else {
    setScreenMetrics(1080, 2340); //默认分辨率，以下按键位置基于此分辨率
    var x = [410, 680, 950, 1220, 1490, 1760, 2030];
    var y = [980, 870, 760];
    for (let i = 0; i < 21; i++) {
        xy.push(x[i % 7], y[parseInt(i / 7)])
    }
};

function ran(c) {
    c = c | 20;
    return Math.random() * c - c / 2
};

function pre(item) {
    let items = [],
        keys = item[0], //按下的琴键
        pressTime = item[1], //按压时间
        sleepTime = item[2] - item[1] > 0 ? item[2] - item[1] : 0; //停顿时间
    for (let index in keys) {
        let id = keys[index],
            x = xy[id * 2 - 2] + ran(),
            y = xy[id * 2 - 1] + ran();
        items.push([pressTime / speedControl, [x, y],
        [x, y]
        ])
    };
    if (items.length > 0) {
        gestures.apply(null, items)
    };
    sleep(sleepTime / speedControl);
}

// Note mapping: low_octave_note = key; middle_octave_note = key+7;
// Chromatic notes like b3 and #5 have been substituted with the nearest diatonic note (2 and 5 respectively).
list = [
    // Bar 1: 2- 3 0
    [[2], pT, t2], // 2. (half)
    [[10], pT, t0], // 3 (quarter)
    [[], pT, t0],   // 0 (quarter rest)
    // Bar 2: 0 1 1 1
    [[], pT, t0],   // 0 (quarter rest)
    [[8], pT, t0],  // 1 (quarter)
    [[8], pT, t0],  // 1 (quarter)
    [[8], pT, t0],  // 1 (quarter)
    // Bar 3 [2]: 3 3 3 6 2 2 (interpreted as six 8th notes + 1 quarter rest)
    [[10], pT, t1], // 3 (8th)
    [[10], pT, t1], // 3 (8th)
    [[10], pT, t1], // 3 (8th)
    [[13], pT, t1], // 6 (8th)
    [[9], pT, t1],  // 2 (8th)
    [[9], pT, t1],  // 2 (8th)
    [[], pT, t0],   // Rest to complete bar
    // Bar 4: 0 6 7 (interpreted as 1q rest, two q notes + 1q rest)
    [[], pT, t0],   // 0 (quarter rest)
    [[13], pT, t0], // 6 (quarter)
    [[14], pT, t0], // 7 (quarter)
    [[], pT, t0],   // Rest to complete bar
    // Bar 5 [3]: 1 6. 1 1 2 b3 (b3 -> 2)
    [[8], pT, t1],  // 1 (8th)
    [[6], pT, t1],  // 6. (8th)
    [[8], pT, t1],  // 1 (8th)
    [[8], pT, t1],  // 1 (8th)
    [[9], pT, t0],  // 2 (quarter)
    [[9], pT, t0],  // b3 -> 2 (quarter)
    // Bar 6: 2 1 1 2
    [[9], pT, t0],  // 2 (quarter)
    [[8], pT, t0],  // 1 (quarter)
    [[8], pT, t0],  // 1 (quarter)
    [[9], pT, t0],  // 2 (quarter)
    // Bar 7 [4]: 3 - 0 (interpreted as half note, 2 quarter rests)
    [[10], pT, t2], // 3 (half)
    [[], pT, t0],   // 0 (quarter rest)
    [[], pT, t0],   // Added rest
    // Bar 8: 0. 1 2 1
    [[], pT, t0],   // 0 (quarter rest)
    [[8], pT, t0],  // 1 (quarter)
    [[9], pT, t0],  // 2 (quarter)
    [[8], pT, t0],  // 1 (quarter)
    // Bar 9 [5]: 3 5. 5 0
    [[10], pT, t0],       // 3 (quarter)
    [[5], pT, t0 * 1.5], // 5. (dotted quarter)
    [[12], pT, t1],      // 5 (8th)
    [[], pT, t0],       // 0 (quarter rest)
    // Bar 10: 0 1 2 1
    [[], pT, t0],   // 0 (quarter rest)
    [[8], pT, t0],  // 1 (quarter)
    [[9], pT, t0],  // 2 (quarter)
    [[8], pT, t0],  // 1 (quarter)
    // Bar 11 [6]: 3 3 3 4 7 7 (interpreted as four 8ths, two quarters)
    [[10], pT, t1], // 3 (8th)
    [[10], pT, t1], // 3 (8th)
    [[10], pT, t1], // 3 (8th)
    [[11], pT, t1], // 4 (8th)
    [[14], pT, t0], // 7 (quarter)
    [[14], pT, t0], // 7 (quarter)
    // Bar 12: 7 1 2 1
    [[14], pT, t0], // 7 (quarter)
    [[8], pT, t0],  // 1 (quarter)
    [[9], pT, t0],  // 2 (quarter)
    [[8], pT, t0],  // 1 (quarter)
    // Bar 13 [7]: 3 2 2 2 1 1 1 7. (eight 8th notes)
    [[10], pT, t1], // 3 (8th)
    [[9], pT, t1],  // 2 (8th)
    [[9], pT, t1],  // 2 (8th)
    [[9], pT, t1],  // 2 (8th)
    [[8], pT, t1],  // 1 (8th)
    [[8], pT, t1],  // 1 (8th)
    [[8], pT, t1],  // 1 (8th)
    [[7], pT, t1],  // 7. (8th)
    // Bar 14: 7. 6. 5. 6.
    [[7], pT, t0],  // 7. (quarter)
    [[6], pT, t0],  // 6. (quarter)
    [[5], pT, t0],  // 5. (quarter)
    [[6], pT, t0],  // 6. (quarter)
    // Bar 15 [8]: 6. 0 .6 1
    [[6], pT, t0],  // 6. (quarter)
    [[], pT, t0],   // 0 (quarter rest)
    [[6], pT, t0],  // .6 (quarter)
    [[8], pT, t0],  // 1 (quarter)
    // Bar 16: 6. 3 2 2
    [[6], pT, t0], // 6. (quarter)
    [[10], pT, t0], // 3 (quarter)
    [[9], pT, t0],  // 2 (quarter)
    [[9], pT, t0],  // 2 (quarter)
    // Bar 17: 1 6. 5
    [[8], pT, t0],  // 1 (quarter)
    [[6], pT, t0],  // 6. (quarter)
    [[12], pT, t0], // 5 (quarter)
    [[], pT, t0],   // Added rest
    // Bar 18 [9]: 2 3 4 5
    [[9], pT, t0],  // 2 (quarter)
    [[10], pT, t0], // 3 (quarter)
    [[11], pT, t0], // 4 (quarter)
    [[12], pT, t0], // 5 (quarter)
    // Bar 19 [10]: 5 3 2-
    [[12], pT, t0], // 5 (quarter)
    [[10], pT, t0], // 3 (quarter)
    [[9], pT, t2],  // 2- (half)
    // Bar 20: 1--1 (Tied half notes, whole note)
    [[8], pT, t2 * 2], // 1 (whole)
    // Bar 21 [11]: #5 3 2. (#5 -> 5)
    [[12], pT, t0], // #5 -> 5 (quarter)
    [[10], pT, t0], // 3 (quarter)
    [[2], pT, t0],  // 2. (quarter)
    [[], pT, t0],   // Added rest
    // Bar 22: 1 1 2 3. 6
    [[8], pT, t1], // 1 (8th)
    [[8], pT, t1], // 1 (8th)
    [[9], pT, t1], // 2 (8th)
    [[10], pT, t1], // 3 (8th)
    [[13], pT, t2], // 6 (half)
    // Bar 23 [12]: 1-
    [[8], pT, t2], // 1- (half)
    [[], pT, t2], // Rest
    // Bar 24: 0 2 3. 6
    [[], pT, t0], // 0 (quarter rest)
    [[9], pT, t1], // 2 (8th)
    [[10], pT, t1], // 3 (8th)
    [[13], pT, t2], // 6 (half)
    // Bar 25: 1. 5 4 4 3 2 2
    [[8], pT, t0 * 1.5], // 1. (dotted quarter)
    [[12], pT, t1], // 5 (8th)
    [[11], pT, t1], // 4 (8th)
    [[11], pT, t1], // 4 (8th)
    [[10], pT, t1], // 3 (8th)
    [[9], pT, t1], // 2 (8th)
    [[9], pT, t0], // 2 (quarter)
    // Bar 26 [14]: 5 3 2-
    [[12], pT, t0], // 5 (quarter)
    [[10], pT, t0], // 3 (quarter)
    [[9], pT, t2], // 2- (half)
    // Bar 27: 1--1 (whole note)
    [[8], pT, t2 * 2],
    // Bar 28 [15]: #5 3 2.
    [[12], pT, t0], // #5 -> 5 (quarter)
    [[10], pT, t0], // 3 (quarter)
    [[2], pT, t0], // 2. (quarter)
    [[], pT, t0],   // Added rest
    // Bar 29: 1 1 2 3. 6
    [[8], pT, t1], // 1 (8th)
    [[8], pT, t1], // 1 (8th)
    [[9], pT, t1], // 2 (8th)
    [[10], pT, t1], // 3 (8th)
    [[13], pT, t2], // 6 (half)
    // Bar 30 [16]: 1- 0 0
    [[8], pT, t2], // 1- (half)
    [[], pT, t0],  // 0 (quarter rest)
    [[], pT, t0],  // 0 (quarter rest)
    // Bar 31 [17]: 0 0 0 (2 3 6)
    [[], pT, t0], // 0
    [[], pT, t0], // 0
    [[], pT, t0], // 0
    [[9], pT, t1], // 2 (8th triplet)
    [[10], pT, t1], // 3 (8th triplet)
    [[13], pT, t1], // 6 (8th triplet) - adjusted timing for triplets
    // Bar 32: 1----
    [[8], pT, t2 * 2], // 1 (whole note)
];

if (sW == true) {
    sleep(100);
    var window = floaty.window('<frame><vertical><button id="btn" text="暂停"/><horizontal><button id="speedLow" text="减速" w="80"/><button id="speedHigh" text="加速"w="80"/></horizontal><horizontal><button id="speed" text="x1" w="80"/><button id="stop" text="停止"w="80"/></horizontal><seekbar id="seek"/><text text="00:00/00:00" background="#FF5A5A5C" gravity="center" id="jd"/></vertical></frame>');
    window.exitOnClose();
    window.btn.click(() => {
        if (window.btn.getText() != '暂停') {
            s = 1;
            window.btn.setText('暂停')
        } else {
            s = 0;
            window.btn.setText('继续')
        }
    })
    window.speedHigh.click(() => {
        speedControl = (speedControl * 10 + 1) / 10;
        window.speed.setText("x" + speedControl)
    })
    window.speedLow.click(() => {
        if (speedControl <= 0.1) {
            return
        };
        speedControl = (speedControl * 10 - 1) / 10;
        window.speed.setText("x" + speedControl)
    })
    window.speed.click(() => {
        speedControl = 1;
        window.speed.setText("x" + speedControl)
    })
    window.stop.click(() => {
        engines.stopAll()
    })
    window.seek.setMax(list.length)
    window.seek.setOnSeekBarChangeListener(new android.widget.SeekBar.OnSeekBarChangeListener({
        onProgressChanged: function (sb, p) {
            progressNow = p;
            window.jd.setText(timeSum(p) + "/" + timeSum(sb.getMax()));
        },
    }))

    function timeSum(p) {
        let timeTotal = 0;
        for (var i = 0; i < p; i++) {
            timeTotal += (list[i][1] == pT ? list[i][2] : list[i][1] + list[i][2]) / speedControl;
        }
        let minute = 0;
        let second = timeTotal / 1000;
        if (second > 59) {
            minute = parseInt(second / 60);
            second = second % 60;
        };
        return (Array(2).join(0) + minute.toFixed(0)).slice(-2) + ":" + (Array(2).join(0) + second.toFixed(0)).slice(-2)
    }
    window.jd.setText("00:00/" + timeSum(list.length))
}
for (var i = 0; i <= list.length; i++) {
    if (sW == true) {
        if (i != progressNow) {
            i = progressNow;
        } else {
            window.seek.setProgress(i)
        };
        if (i >= list.length || i <= 0) {
            i = s = progressNow = 0;
            window.btn.setText('继续');
            window.seek.setProgress(0);
            while (s != 1) {
                sleep(100)
            };
        }
    } else {
        if (i >= list.length - 1) {
            exit()
        };
    };
    pre(list[i]);
    progressNow++;
    while (s != 1) {
        sleep(100)
    };
}