//以下为乐谱转js专有模板
let t0 = 393,//默认间隔-
    t1 = 196,//较短间隔~
    t2 = 786,//较长间隔,
    t3 = 100,//固定间隔*
    pT = 35,//默认按压时间
    sW = true;//是否显示悬浮按钮
let s = 1, progressNow=0,speedControl = 1,xy = [],zuobiaoPath = "/sdcard/脚本/zuobiao21.txt";
if (files.exists(zuobiaoPath)) { //如果机型适配过
    eval(files.read(zuobiaoPath)); //快速适配分辨率
} else {
    setScreenMetrics(1080, 2340); //默认分辨率，以下按键位置基于此分辨率
    var x = [410, 680, 950, 1220, 1490, 1760, 2030];
    var y = [980, 870, 760];
    for (let i = 0; i < 21; i++) {
        xy.push(x[i % 7], y[parseInt(i / 7)])
    }
};
function ran(c) {c=c|20;return Math.random() * c - c / 2};
function pre(item) {
    let items = [],
        keys = item[0],//按下的琴键
        pressTime = item[1],//按压时间
        sleepTime = item[2]-item[1]>0?item[2]-item[1]:0;//停顿时间
    for (let index in keys) {
        let id = keys[index],
            x = xy[id * 2 - 2] + ran(),
            y = xy[id * 2 - 1] + ran();
        items.push([pressTime / speedControl, [x, y],[x, y]])
    };
    if(items.length>0){gestures.apply(null, items)};
    sleep(sleepTime / speedControl);
}
list = [
[[1,5],pT,t2],
[[3,5],pT,t2],
[[3,5],pT,t2],
[[1,3],pT,t1],
[[2],pT,t1],
[[1],pT,t0],
[[1,4,6],pT,t2],
[[1,4],pT,t2],
[[1,4,7],pT,t2],
[[4,6],pT,t2],
[[1,3,5],pT,t2],
[[1,3],pT,t2],
[[3,5],pT,t2],
[[1,3],pT,t1],
[[2],pT,t1],
[[1],pT,t0],
[[1,2],pT,t2],
[[1],pT,t2],
[[1,4],pT,t2],
[[1,3],pT,t2],
[[1,5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[1,5,10],pT,t0],
[[9],pT,t1],
[[9],pT,t1],
[[3,5],pT,t0],
[[8],pT,t0],
[[1,5,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[3,5,10],pT,t0],
[[9],pT,t1],
[[9],pT,t1],
[[3,5],pT,t0],
[[8],pT,t1],
[[8],pT,t1],
[[1,4],pT,t1],
[[6],pT,t1],
[[4],pT,t1],
[[6],pT,t1],
[[4,6,8],pT,t1],
[[6],pT,t1],
[[4],pT,t1],
[[5],pT,t1],
[[3,5,10],pT,t0],
[[9],pT,t1],
[[9],pT,t1],
[[3,5],pT,t0],
[[8],pT,t1],
[[11],pT,t1],
[[1,5],pT,t0],
[[10],pT,t0],
[[3,5],pT,t0+t1],
[[8],pT,t1],
[[3,5,8,12],pT,t0],
[[10],pT,t1],
[[9],pT,t1],
[[3,5],pT,t0],
[[8],pT,t0],
[[1,4,6],pT,t1],
[[6],pT,t1],
[[4],pT,t1],
[[6],pT,t1],
[[1,4,8],pT,t1],
[[6],pT,t1],
[[4],pT,t1],
[[6],pT,t1],
[[4,6,8,12],pT,t0],
[[10],pT,t1],
[[9],pT,t1],
[[1,4],pT,t0],
[[8],pT,t1],
[[9],pT,t1],
[[1,4],pT,t2],
[[4,6],pT,t2],
[[2,5,7],pT,t2],
[[2,5],pT,t2],
[[1,5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[1,5,10],pT,t0],
[[9],pT,t1],
[[9],pT,t1],
[[3,5],pT,t0],
[[8],pT,t0],
[[1,5,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[3,6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[3,5,10],pT,t0],
[[9],pT,t1],
[[9],pT,t1],
[[3,5],pT,t0],
[[8],pT,t1],
[[8],pT,t1],
[[3,6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[3,6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[3,5,8,10],pT,t0],
[[9],pT,t1],
[[9],pT,t1],
[[3,5],pT,t0],
[[8],pT,t1],
[[11],pT,t1],
[[1,5],pT,t0],
[[10],pT,t0],
[[3,5],pT,t0+t1],
[[8],pT,t1],
[[3,5,8,12],pT,t0],
[[10],pT,t1],
[[9],pT,t1],
[[3,5],pT,t0],
[[8],pT,t0],
[[1,4,6],pT,t1],
[[6],pT,t1],
[[4],pT,t1],
[[6],pT,t1],
[[4,8],pT,t1],
[[6],pT,t1],
[[4],pT,t1],
[[6],pT,t1],
[[4,8,12],pT,t0],
[[10],pT,t1],
[[9],pT,t1],
[[1,4],pT,t0],
[[8],pT,t0],
[[4,6,8],pT,t1],
[[6],pT,t1],
[[4],pT,t1],
[[6],pT,t1],
[[4,6,8],pT,t1],
[[6],pT,t1],
[[4],pT,t1],
[[6],pT,t1],
[[4,6,8,12],pT,t0],
[[10],pT,t1],
[[9],pT,t1],
[[1,4],pT,t0],
[[8],pT,t0],
[[1,3,6],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[8],pT,t1],
[[9],pT,t1],
[[3,6,10],pT,t0],
[[10],pT,t1],
[[10],pT,t1],
[[3,6],pT,t0],
[[11],pT,t0],
[[3,5,10],pT,t2],
[[3,5],pT,t0],
[[9],pT,t1],
[[10],pT,t1],
[[1,4,6,11],pT,t0],
[[10],pT,t1],
[[9],pT,t1],
[[4,6],pT,t0],
[[8],pT,t1],
[[8],pT,t1],
[[1,4,6],pT,t1],
[[10],pT,t0],
[[9],pT,t1],
[[4,6],pT,t2],
[[2,5,7],pT,t1],
[[5],pT,t1],
[[4],pT,t1],
[[5],pT,t1],
[[5,7],pT,t1],
[[5],pT,t1],
[[4],pT,t1],
[[5],pT,t1],
[[2,5,9],pT,t0],
[[8],pT,t1],
[[9],pT,t1],
[[5,7],pT,t0],
[[8],pT,t0],
[[1,5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[3,6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[3,5,8],pT,t0],
[[9],pT,t0],
[[3,5,8,10],pT,t0],
[[12],pT,t0],
[[4,6,13],pT,t0],
[[12],pT,t0],
[[1,4,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t0],
[[12],pT,t0],
[[1,4,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t2],
[[1,4],pT,t2],
[[4,6,8,13],pT,t0],
[[12],pT,t0],
[[1,4,8,15],pT,t0],
[[10],pT,t0],
[[1,5],pT,t0],
[[9],pT,t0],
[[3,5,8,12],pT,t0],
[[10],pT,t0],
[[1,5],pT,t0],
[[9],pT,t0],
[[3,5,8,12],pT,t0],
[[10],pT,t0],
[[3,6],pT,t2],
[[3,6],pT,t2],
[[3,5,8],pT,t0],
[[9],pT,t0],
[[3,5,8,10],pT,t0],
[[12],pT,t0],
[[4,6,13],pT,t0],
[[12],pT,t0],
[[1,4,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t0],
[[12],pT,t0],
[[1,4,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t0],
[[12],pT,t0],
[[1,4],pT,t0],
[[11],pT,t0],
[[4,6],pT,t0],
[[8],pT,t0],
[[4,6,9],pT,t0],
[[8],pT,t0],
[[1,5],pT,t0],
[[5],pT,t0],
[[1],pT,t0],
[[5],pT,t0],
[[1,3],pT,t0],
[[10],pT,t0],
[[3,5,9],pT,t0],
[[8],pT,t0],
[[3,6],pT,t2],
[[3,6],pT,t2],
[[3,5,8],pT,t0],
[[9],pT,t0],
[[5,10],pT,t0],
[[6],pT,t0],
[[1,4],pT,t0],
[[9],pT,t0],
[[4,6],pT,t2],
[[1,4,8],pT,t0],
[[9],pT,t0],
[[4,6,10],pT,t0],
[[11],pT,t0],
[[2,5],pT,t1],
[[10],pT,t1],
[[9],pT,t0],
[[5,7],pT,t2],
[[2,5,10],pT,t2],
[[5,7,9],pT,t1],
[[8],pT,t0+t1],
[[1,5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[1,5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[1,5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[3,6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[1,5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[1,5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[3,6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[3,6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[1,5,10],pT,t2],
[[3,5],pT,t0+t1],
[[5],pT,t1],
[[1,5,10],pT,t0],
[[9],pT,t1],
[[9],pT,t1],
[[3,5],pT,t0],
[[8],pT,t1],
[[8],pT,t1],
[[1,5],pT,t2],
[[3,5],pT,t0+t1],
[[5],pT,t1],
[[1,5,10],pT,t0],
[[9],pT,t1],
[[9],pT,t1],
[[3,5],pT,t0],
[[8],pT,t1],
[[8],pT,t1],
[[3,6],pT,t2],
[[3,5],pT,t0+t1],
[[5],pT,t1],
[[3,5,8,10],pT,t0],
[[9],pT,t1],
[[9],pT,t1],
[[3,5],pT,t0],
[[8],pT,t1],
[[11],pT,t1],
[[1,5],pT,t0],
[[10],pT,t0],
[[3,5],pT,t0+t1],
[[8],pT,t1],
[[3,5,8,12],pT,t0],
[[10],pT,t1],
[[9],pT,t1],
[[1,5],pT,t0],
[[8],pT,t0],
[[1,4],pT,t0],
[[6],pT,t0],
[[1,4],pT,t2],
[[4,6,8,12],pT,t0],
[[10],pT,t1],
[[9],pT,t1],
[[1,4],pT,t0],
[[8],pT,t0],
[[4,6,8],pT,t2],
[[1,4],pT,t2],
[[4,6,8,12],pT,t0],
[[10],pT,t1],
[[9],pT,t1],
[[1,4],pT,t0],
[[8],pT,t0],
[[1,3,6],pT,t1],
[[5],pT,t0+t1],
[[1,3],pT,t2],
[[3,5],pT,t2],
[[3,5],pT,t1],
[[5],pT,t1],
[[8],pT,t1],
[[9],pT,t1],
[[3,6,10],pT,t0],
[[10],pT,t1],
[[10],pT,t1],
[[3,6],pT,t0],
[[11],pT,t0],
[[3,5,8,10],pT,t2],
[[3,5],pT,t0],
[[9],pT,t1],
[[10],pT,t1],
[[1,4,6,11],pT,t0],
[[10],pT,t1],
[[9],pT,t1],
[[4,6],pT,t0],
[[8],pT,t0],
[[1,4,8],pT,t1],
[[10],pT,t0],
[[9],pT,t1],
[[4,6],pT,t2],
[[2,5],pT,t2],
[[5,7],pT,t2],
[[2,5,9],pT,t0],
[[8],pT,t1],
[[9],pT,t1],
[[5,7],pT,t0],
[[8],pT,t1],
[[8],pT,t1],
[[1,5],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[8],pT,t1],
[[3,5],pT,t2],
[[1,5,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[8],pT,t1],
[[1,3],pT,t2],
[[1,4,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[8],pT,t1],
[[3,5],pT,t2],
[[3,5,8],pT,t0],
[[9],pT,t0],
[[5,8,10],pT,t0],
[[12],pT,t0],
[[4,6,13],pT,t0],
[[1,12],pT,t0],
[[4,6,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t0],
[[1,12],pT,t0],
[[4,6,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t0],
[[1],pT,t0],
[[4,6],pT,t2],
[[4,6,8,13],pT,t0],
[[1,12],pT,t0],
[[4,6,8,15],pT,t0],
[[10],pT,t0],
[[3,5],pT,t0],
[[1,9],pT,t0],
[[3,5,8,12],pT,t0],
[[10],pT,t0],
[[3,5],pT,t0],
[[1,9],pT,t0],
[[3,5,8,12],pT,t0],
[[10],pT,t0],
[[6,8],pT,t0],
[[3],pT,t0],
[[6,8],pT,t2],
[[5,8],pT,t0],
[[3,9],pT,t0],
[[5,10],pT,t0],
[[12],pT,t0],
[[4,6,13],pT,t0],
[[1,12],pT,t0],
[[4,6,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t0],
[[1,12],pT,t0],
[[4,6,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t0],
[[1,12],pT,t0],
[[4,6],pT,t0],
[[11],pT,t0],
[[4,6],pT,t0],
[[1,8],pT,t0],
[[4,6,9],pT,t0],
[[8],pT,t0],
[[3,5],pT,t0],
[[5],pT,t0],
[[1],pT,t0],
[[5],pT,t0],
[[3,5],pT,t0],
[[10],pT,t0],
[[3,5,9],pT,t0],
[[8],pT,t0],
[[6],pT,t0],
[[3],pT,t0],
[[6,8],pT,t2],
[[5,8],pT,t0],
[[3,9],pT,t0],
[[5,10],pT,t0],
[[6],pT,t0],
[[4],pT,t0],
[[1,8],pT,t0],
[[4,6],pT,t2],
[[4,8],pT,t0],
[[1,9],pT,t0],
[[4,6,10],pT,t0],
[[11],pT,t0],
[[5,7],pT,t1],
[[10],pT,t1],
[[2,9],pT,t0],
[[5,7],pT,t2],
[[5,7,10],pT,t0],
[[2,12],pT,t0],
[[5,9],pT,t0],
[[8],pT,t0],
[[1,5],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[8],pT,t1],
[[3,5],pT,t2],
[[1,5],pT,t1],
[[4],pT,t1],
[[5],pT,t1],
[[8],pT,t1],
[[1,4],pT,t2],
[[3,6],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[8],pT,t1],
[[1,3],pT,t2],
[[5,8],pT,t0],
[[9],pT,t0],
[[3,5,8,10],pT,t0],
[[12],pT,t0],
[[4,6,8,13],pT,t0],
[[1,12],pT,t0],
[[4,6,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t0],
[[1,12],pT,t0],
[[4,6,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t0],
[[1],pT,t0],
[[4,6],pT,t2],
[[4,6,8,13],pT,t0],
[[1,12],pT,t0],
[[4,6,8,15],pT,t0],
[[10],pT,t0],
[[3,5],pT,t0],
[[1,9],pT,t0],
[[3,5,8,12],pT,t0],
[[10],pT,t0],
[[3,5],pT,t0],
[[1,9],pT,t0],
[[3,5,8,12],pT,t0],
[[10],pT,t0],
[[6,8],pT,t0],
[[3],pT,t0],
[[6,8],pT,t2],
[[5,8],pT,t0],
[[3,9],pT,t0],
[[5,8,10],pT,t0],
[[12],pT,t0],
[[4,6,13],pT,t0],
[[1,12],pT,t0],
[[4,6,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t0],
[[1,12],pT,t0],
[[4,6,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t0],
[[1,12],pT,t0],
[[4,6],pT,t0],
[[11],pT,t0],
[[4,6],pT,t0],
[[1,8],pT,t0],
[[4,6,9],pT,t0],
[[8],pT,t0],
[[3,5],pT,t0],
[[1,5],pT,t0],
[[3],pT,t0],
[[3,5],pT,t0],
[[1],pT,t0],
[[5,8],pT,t0],
[[3,5,9],pT,t0],
[[10],pT,t0],
[[6],pT,t0],
[[3,9],pT,t0],
[[6],pT,t0],
[[8],pT,t0],
[[5,8,15],pT,t0],
[[3,14],pT,t0],
[[5,8,13],pT,t0],
[[12],pT,t0],
[[4,6,13],pT,t0],
[[1,12],pT,t0],
[[4,6,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t0],
[[1,12],pT,t0],
[[4,6,10],pT,t0],
[[13],pT,t0],
[[4,6],pT,t0],
[[1],pT,t0],
[[4,6],pT,t2],
[[4,6,8,13],pT,t0],
[[1,12],pT,t0],
[[4,8,15],pT,t0],
[[10],pT,t0],
[[3,5],pT,t0],
[[1,9],pT,t0],
[[3,5,8,12],pT,t0],
[[10],pT,t0],
[[3,5],pT,t0],
[[1,9],pT,t0],
[[3,5,9],pT,t0],
[[8],pT,t0],
[[6],pT,t0],
[[3],pT,t0],
[[6,8],pT,t2],
[[5,8],pT,t0],
[[3,9],pT,t0],
[[5,10],pT,t0],
[[6],pT,t0],
[[4],pT,t0],
[[1,8],pT,t0],
[[4,6],pT,t2],
[[4,8],pT,t0],
[[1,9],pT,t0],
[[4,6,10],pT,t0],
[[11],pT,t0],
[[5,7],pT,t1],
[[10],pT,t1],
[[2,9],pT,t0],
[[5,7],pT,t2],
[[5,7,10],pT,t0],
[[2],pT,t0],
[[5,9],pT,t0],
[[8],pT,t0],
[[1,5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[1,5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[5,8],pT,t1],
[[5],pT,t1],
[[3],pT,t1],
[[5],pT,t1],
[[3,6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[3,6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[6,8],pT,t1],
[[6],pT,t1],
[[3],pT,t1],
[[6],pT,t1],
[[1,5,8],pT,t1],
[[7],pT,t1],
[[3],pT,t1],
[[7],pT,t1],
[[5,8],pT,t1],
[[7],pT,t1],
[[3],pT,t1],
[[7],pT,t1],
[[1,5,8],pT,t1],
[[7],pT,t1],
[[3],pT,t1],
[[7],pT,t1],
[[5,8],pT,t1],
[[7],pT,t1],
[[3],pT,t1],
[[7],pT,t1],
[[],pT,0],

]
if(sW==true){
    sleep(100);var window = floaty.window('<frame><vertical><button id="btn" text="暂停"/><horizontal><button id="speedLow" text="减速" w="80"/><button id="speedHigh" text="加速"w="80"/></horizontal><horizontal><button id="speed" text="x1" w="80"/><button id="stop" text="停止"w="80"/></horizontal><seekbar id="seek"/><text text="00:00/00:00" background="#FF5A5A5C" gravity="center" id="jd"/></vertical></frame>');window.exitOnClose();
    window.btn.click(()=>{if (window.btn.getText() != '暂停') {s = 1;window.btn.setText('暂停')} else {s = 0;window.btn.setText('继续')}})
    window.speedHigh.click(()=>{speedControl=(speedControl*10+1)/10;window.speed.setText("x"+speedControl)})
    window.speedLow.click(()=>{if(speedControl<=0.1){return};speedControl=(speedControl*10-1)/10;window.speed.setText("x"+speedControl)})
    window.speed.click(()=>{speedControl=1;window.speed.setText("x"+speedControl)})
    window.stop.click(()=>{engines.stopAll()})
    window.seek.setMax(list.length)
    window.seek.setOnSeekBarChangeListener(new android.widget.SeekBar.OnSeekBarChangeListener({	onProgressChanged: function(sb, p) {progressNow=p;window.jd.setText(timeSum(p)+"/"+timeSum(sb.getMax()));	},}))
    function timeSum(p){let timeTotal=0;for(var i=0;i<p;i++){timeTotal+=(list[i][1]==pT?list[i][2]:list[i][1]+list[i][2])/speedControl;}let minute = 0;let second = timeTotal/1000;if (second>59) {minute = parseInt(second / 60);second = second % 60;};return  (Array(2).join(0) + minute.toFixed(0)).slice(-2)+":"+ (Array(2).join(0) + second.toFixed(0)).slice(-2)}
    window.jd.setText("00:00/"+timeSum(list.length))
}
for(var i=0;i<=list.length;i++){
   if (sW==true){
       if (i!=progressNow){i=progressNow;}else{window.seek.setProgress(i)};
       if (i>=list.length||i<=0){i=s=progressNow=0;window.btn.setText('继续');window.seek.setProgress(0);while (s != 1){sleep(100)};}
   }else{
       if (i>=list.length-1){exit()};
   };
   pre(list[i]);progressNow++;
   while (s != 1){sleep(100)};
}


