//以下为乐谱转js专有模板
let t0 = 500,//默认间隔-（四分音符）
    t1 = 250,//较短间隔~（八分音符）
    t2 = 1000,//较长间隔,（二分音符）
    t3 = 100,//固定间隔*
    pT = 35,//默认按压时间
    sW = true;//是否显示悬浮按钮
let s = 1, progressNow=0,speedControl = 1,xy = [],zuobiaoPath = "/sdcard/脚本/zuobiao21.txt";
if (files.exists(zuobiaoPath)) { //如果机型适配过
    eval(files.read(zuobiaoPath)); //快速适配分辨率
} else {
    setScreenMetrics(1080, 2340); //默认分辨率，以下按键位置基于此分辨率
    var x = [410, 680, 950, 1220, 1490, 1760, 2030];
    var y = [980, 870, 760];
    for (let i = 0; i < 21; i++) {
        xy.push(x[i % 7], y[parseInt(i / 7)])
    }
};
function ran(c) {c=c|20;return Math.random() * c - c / 2};
function pre(item) {
    let items = [],
        keys = item[0],//按下的琴键
        pressTime = item[1],//按压时间
        sleepTime = item[2]-item[1]>0?item[2]-item[1]:0;//停顿时间
    for (let index in keys) {
        let id = keys[index],
            x = xy[id * 2 - 2] + ran(),
            y = xy[id * 2 - 1] + ran();
        items.push([pressTime / speedControl, [x, y],[x, y]])
    };
    if(items.length>0){gestures.apply(null, items)};
    sleep(sleepTime / speedControl);
}
list = [
// 第1小节: 2. 3 0 0 0 1 1 1
[[2],pT,t2], // 2.（二分音符）
[[3],pT,t0], // 3（四分音符）
[[],pT,t0], // 0（四分音符休止符）
[[],pT,t0], // 0（四分音符休止符）
[[],pT,t0], // 0（四分音符休止符）
[[1],pT,t0], // 1（四分音符）
[[1],pT,t0], // 1（四分音符）
[[1],pT,t0], // 1（四分音符）

// 第2小节: 3 3 3 3 6 2 2 0 6 7
[[3],pT,t0], // 3（四分音符）
[[3],pT,t0], // 3（四分音符）
[[3],pT,t0], // 3（四分音符）
[[3],pT,t0], // 3（四分音符）
[[6],pT,t1], // 6（八分音符）
[[2],pT,t0], // 2（四分音符）
[[2],pT,t0], // 2（四分音符）
[[],pT,t0], // 0（四分音符休止符）
[[6],pT,t1], // 6（八分音符）
[[7],pT,t1], // 7（八分音符）

// 第3小节: 1 6 1 1 2 b3 2 1 1 2
[[1],pT,t1], // 1（八分音符）
[[6],pT,t0], // 6（四分音符）
[[1],pT,t0], // 1（四分音符）
[[1],pT,t0], // 1（四分音符）
[[2],pT,t0], // 2（四分音符）
[[10],pT,t1], // b3（八分音符，用10表示低音3）
[[2],pT,t0], // 2（四分音符）
[[1],pT,t0], // 1（四分音符）
[[1],pT,t0], // 1（四分音符）
[[2],pT,t0], // 2（四分音符）

// 第4小节: 3 - 0 0. 1 2 1
[[3],pT,t0], // 3（四分音符）
[[],pT,t0], // -（四分音符休止符）
[[],pT,t0], // 0（四分音符休止符）
[[],pT,t1+t0], // 0.（附点四分音符休止符）
[[1],pT,t0], // 1（四分音符）
[[2],pT,t1], // 2（八分音符）
[[1],pT,t0], // 1（四分音符）

// 第5小节: 3 5. 5 0 0 1 2 1
[[3],pT,t0], // 3（四分音符）
[[5],pT,t1+t0], // 5.（附点四分音符）
[[5],pT,t0], // 5（四分音符）
[[],pT,t0], // 0（四分音符休止符）
[[],pT,t0], // 0（四分音符休止符）
[[1],pT,t0], // 1（四分音符）
[[2],pT,t1], // 2（八分音符）
[[1],pT,t0], // 1（四分音符）

// 第6小节: 3 3 3 3 4 7 7 7 1 2 1
[[3],pT,t0], // 3（四分音符）
[[3],pT,t0], // 3（四分音符）
[[3],pT,t0], // 3（四分音符）
[[3],pT,t0], // 3（四分音符）
[[4],pT,t1], // 4（八分音符）
[[7],pT,t1], // 7（八分音符）
[[7],pT,t1], // 7（八分音符）
[[7],pT,t1], // 7（八分音符）
[[1],pT,t0], // 1（四分音符）
[[2],pT,t1], // 2（八分音符）
[[1],pT,t0], // 1（四分音符）

// 第7小节: 3 2 2 2 1 1 1 7 7 6 5 6
[[3],pT,t0], // 3（四分音符）
[[2],pT,t0], // 2（四分音符）
[[2],pT,t0], // 2（四分音符）
[[2],pT,t0], // 2（四分音符）
[[1],pT,t0], // 1（四分音符）
[[1],pT,t0], // 1（四分音符）
[[1],pT,t1], // 1（八分音符）
[[7],pT,t1], // 7（八分音符）
[[7],pT,t1], // 7（八分音符）
[[6],pT,t1], // 6（八分音符）
[[5],pT,t1], // 5（八分音符）
[[6],pT,t1], // 6（八分音符）

// 第8小节: 6 0. 6 1 6 3 2 2 1 6 5 | 2 3 4 5
[[6],pT,t0], // 6（四分音符）
[[],pT,t1+t0], // 0.（附点四分音符休止符）
[[6],pT,t1], // 6（八分音符）
[[1],pT,t1], // 1（八分音符）
[[6],pT,t1], // 6（八分音符）
[[3],pT,t1], // 3（八分音符）
[[2],pT,t1], // 2（八分音符）
[[2],pT,t0], // 2（四分音符）
[[1],pT,t1], // 1（八分音符）
[[6],pT,t1], // 6（八分音符）
[[5],pT,t0], // 5（四分音符）
[[2],pT,t1], // 2（八分音符）
[[3],pT,t1], // 3（八分音符）
[[4],pT,t1], // 4（八分音符）
[[5],pT,t1], // 5（八分音符）

// 第10小节: 5 3 2. 1 1
[[5],pT,t0], // 5（四分音符）
[[3],pT,t0], // 3（四分音符）
[[2],pT,t1+t0], // 2.（附点四分音符）
[[1],pT,t0], // 1（四分音符）
[[1],pT,t0], // 1（四分音符）

// 第11小节: #5 3 2. 1 1 2 3. 6
[[8],pT,t0], // #5（四分音符，用8表示#5）
[[3],pT,t0], // 3（四分音符）
[[2],pT,t1+t0], // 2.（附点四分音符）
[[1],pT,t0], // 1（四分音符）
[[1],pT,t0], // 1（四分音符）
[[2],pT,t0], // 2（四分音符）
[[3],pT,t1+t0], // 3.（附点四分音符）
[[6],pT,t1], // 6（八分音符）

// 第12小节: 1 - 0 0 2 3. 6 | 1. 5 4 4 3 2 2
[[1],pT,t0], // 1（四分音符）
[[],pT,t0], // -（四分音符休止符）
[[],pT,t0], // 0（四分音符休止符）
[[],pT,t0], // 0（四分音符休止符）
[[2],pT,t0], // 2（四分音符）
[[3],pT,t1+t0], // 3.（附点四分音符）
[[6],pT,t1], // 6（八分音符）
[[1],pT,t1+t0], // 1.（附点四分音符）
[[5],pT,t1], // 5（八分音符）
[[4],pT,t1], // 4（八分音符）
[[4],pT,t1], // 4（八分音符）
[[3],pT,t1], // 3（八分音符）
[[2],pT,t1], // 2（八分音符）
[[2],pT,t0], // 2（四分音符）

// 第14小节: 5 3 2. 1 1
[[5],pT,t0], // 5（四分音符）
[[3],pT,t0], // 3（四分音符）
[[2],pT,t1+t0], // 2.（附点四分音符）
[[1],pT,t0], // 1（四分音符）
[[1],pT,t0], // 1（四分音符）

// 第15小节: #5 3 2. 1 1 2 3. 6 | 1 - 0 0
[[8],pT,t0], // #5（四分音符）
[[3],pT,t0], // 3（四分音符）
[[2]
if(sW==true){
    sleep(100);var window = floaty.window('<frame><vertical><button id="btn" text="暂停"/><horizontal><button id="speedLow" text="减速" w="80"/><button id="speedHigh" text="加速"w="80"/></horizontal><horizontal><button id="speed" text="x1" w="80"/><button id="stop" text="停止"w="80"/></horizontal><seekbar id="seek"/><text text="00:00/00:00" background="#FF5A5A5C" gravity="center" id="jd"/></vertical></frame>');window.exitOnClose();
    window.btn.click(()=>{if (window.btn.getText() != '暂停') {s = 1;window.btn.setText('暂停')} else {s = 0;window.btn.setText('继续')}})
    window.speedHigh.click(()=>{speedControl=(speedControl*10+1)/10;window.speed.setText("x"+speedControl)})
    window.speedLow.click(()=>{if(speedControl<=0.1){return};speedControl=(speedControl*10-1)/10;window.speed.setText("x"+speedControl)})
    window.speed.click(()=>{speedControl=1;window.speed.setText("x"+speedControl)})
    window.stop.click(()=>{engines.stopAll()})
    window.seek.setMax(list.length)
    window.seek.setOnSeekBarChangeListener(new android.widget.SeekBar.OnSeekBarChangeListener({	onProgressChanged: function(sb, p) {progressNow=p;window.jd.setText(timeSum(p)+"/"+timeSum(sb.getMax()));	},}))
    function timeSum(p){let timeTotal=0;for(var i=0;i<p;i++){timeTotal+=(list[i][1]==pT?list[i][2]:list[i][1]+list[i][2])/speedControl;}let minute = 0;let second = timeTotal/1000;if (second>59) {minute = parseInt(second / 60);second = second % 60;};return  (Array(2).join(0) + minute.toFixed(0)).slice(-2)+":"+ (Array(2).join(0) + second.toFixed(0)).slice(-2)}
    window.jd.setText("00:00/"+timeSum(list.length))
}
for(var i=0;i<=list.length;i++){
   if (sW==true){
       if (i!=progressNow){i=progressNow;}else{window.seek.setProgress(i)};
       if (i>=list.length||i<=0){i=s=progressNow=0;window.btn.setText('继续');window.seek.setProgress(0);while (s != 1){sleep(100)};}
   }else{
       if (i>=list.length-1){exit()};
   };
   pre(list[i]);progressNow++;
   while (s != 1){sleep(100)};
},pT,t1+t0], // 2.（附点四分音符）
[[1],pT,t0], // 1（四分音符）
[[1],pT,t0], // 1（四分音符）
[[2],pT,t0], // 2（四分音符）
[[3],pT,t1+t0], // 3.（附点四分音符）
[[6],pT,t1], // 6（八分音符）
[[1],pT,t0], // 1（四分音符）
[[],pT,t0], // -（四分音符休止符）
[[],pT,t0], // 0（四分音符休止符）
[[],pT,t0], // 0（四分音符休止符）

// 第17小节: 0 0 0 2 3 6 | 1 - - -
[[],pT,t0], // 0（四分音符休止符）
[[],pT,t0], // 0（四分音符休止符）
[[],pT,t0], // 0（四分音符休止符）
[[2],pT,t1], // 2（八分音符）
[[3],pT,t1], // 3（八分音符）
[[6],pT,t1], // 6（八分音符）
[[1],pT,t0], // 1（四分音符）
[[],pT,t0], // -（四分音符休止符）
[[],pT,t0], // -（四分音符休止符）
[[],pT,t0], // -（四分音符休止符）

]
